import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:audioplayers/audioplayers.dart';

class PuzzlePage extends StatefulWidget {
  @override
  State<PuzzlePage> createState() => _PuzzlePageState();
}

class _PuzzlePageState extends State<PuzzlePage> {
  final AudioPlayer audioPlayer = AudioPlayer();
  int secondsElapsed = 0; // Time counter
  Timer? gameTimer; // Timer object
  int gridSize = 3; // default to 3x3
  int moveCount = 0; // Number of moves
  late Stopwatch stopwatch; // Timer
  ui.Image? fullImage;
  final picker = ImagePicker();
  late List<int> tileOrder; // ترتيب المربعات
  late int emptyTile; // رقم المربع الفارغ

  // Format time
  String formatTime(int seconds) {
    final mins = (seconds ~/ 60).toString().padLeft(2, '0');
    final secs = (seconds % 60).toString().padLeft(2, '0');
    return '$mins:$secs';
  }

  // Pick image and prepare puzzle
  Future<void> pickImage() async {
    final picked = await picker.pickImage(source: ImageSource.gallery);
    if (picked != null) {
      final bytes = await picked.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();
      setState(() {
        fullImage = frame.image;
        tileOrder = List.generate(gridSize * gridSize, (i) => i);
        tileOrder.shuffle(); // Shuffle the puzzle
        secondsElapsed = 0;
        gameTimer?.cancel(); // Cancel if running
        gameTimer = Timer.periodic(Duration(seconds: 1), (timer) {
          setState(() {
            secondsElapsed++;
          });
        });
        emptyTile = tileOrder.last; // last tile is the "empty" one
        stopwatch = Stopwatch()..start();
        moveCount = 0;
      });
    }
  }

  // Check if a tile is adjacent to the empty one
  bool isMovable(int tappedIndex) {
    int tappedRow = tappedIndex ~/ gridSize;
    int tappedCol = tappedIndex % gridSize;
    int emptyIndex = tileOrder.indexOf(emptyTile);
    int emptyRow = emptyIndex ~/ gridSize;
    int emptyCol = emptyIndex % gridSize;

    // Only movable if directly adjacent (up/down/left/right)
    return (tappedRow == emptyRow && (tappedCol - emptyCol).abs() == 1) ||
        (tappedCol == emptyCol && (tappedRow - emptyRow).abs() == 1);
  }

  // Check if the puzzle is solved
  bool isSolved() {
    for (int i = 0; i < tileOrder.length - 1; i++) {
      if (tileOrder[i] != i) return false;
    }
    return true;
  }

  // Swap tapped tile with the empty one
  void moveTile(int index) {
    if (!isMovable(index)) return;

    int emptyIndex = tileOrder.indexOf(emptyTile);
    setState(() {
      final temp = tileOrder[index];
      tileOrder[index] = tileOrder[emptyIndex];
      tileOrder[emptyIndex] = temp;
      moveCount++;

      // ✅ Check for win
      if (isSolved()) {
        stopwatch.stop();
        final duration = stopwatch.elapsed;
        audioPlayer.play(AssetSource('sounds/win.mp3'));

        Future.delayed(Duration(milliseconds: 300), () {
          showDialog(
            context: context,
            builder:
                (_) => AlertDialog(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  backgroundColor: Colors.white,
                  title: Center(
                    child: Text(
                      "🎉 You Win!",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text("👏 Great job!", style: TextStyle(fontSize: 18)),
                      SizedBox(height: 10),
                      Text("Moves: $moveCount"),
                      Text("Time: ${duration.inSeconds} sec"),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        restartGame();
                      },
                      child: Text("Play Again"),
                    ),
                  ],
                ),
          );
        });
      }
    });
    audioPlayer.play(AssetSource('sounds/digital-click.mp3'));
  }

  // Restart the game
  void restartGame() {
    secondsElapsed = 0;
    gameTimer?.cancel();
    gameTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        secondsElapsed++;
      });
    });
    setState(() {
      tileOrder.shuffle();
      emptyTile = tileOrder.last;
      stopwatch.reset();
      moveCount = 0;
      stopwatch = Stopwatch()..start();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: Colors.blueGrey[900],
        title: Text(
          '🧩 Sliding Puzzle',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Moves: $moveCount',
                  style: TextStyle(fontSize: 14, color: Colors.white),
                ),
                Text(
                  'Time: ${formatTime(secondsElapsed)}',
                  style: TextStyle(fontSize: 14, color: Colors.white),
                ),
              ],
            ),
          ),
        ],
      ),

      body: Center(
        child:
            fullImage == null
                ? Column(
                  children: [
                    DropdownButton<int>(
                      value: gridSize,
                      items:
                          [3, 4, 5].map((size) {
                            return DropdownMenuItem<int>(
                              value: size,
                              child: Text('$size x $size'),
                            );
                          }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            gridSize = value;
                          });
                        }
                      },
                    ),
                    SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: pickImage,
                      child: Text("Pick Image"),
                    ),
                  ],
                )
                : AspectRatio(
                  aspectRatio: 1,
                  child: GridView.builder(
                    itemCount: gridSize * gridSize,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: gridSize,
                    ),
                    itemBuilder: (context, index) {
                      int tileNumber = tileOrder[index];
                      final tileWidth = fullImage!.width / gridSize;
                      final tileHeight = fullImage!.height / gridSize;
                      final dx = (tileNumber % gridSize) * tileWidth;
                      final dy = (tileNumber ~/ gridSize) * tileHeight;

                      // If this is the empty tile, show a blank container
                      if (tileNumber == emptyTile) {
                        return Container(color: Colors.grey[300]);
                      }

                      return GestureDetector(
                        onTap: () => moveTile(index),
                        child: Container(
                          margin: EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(
                              12,
                            ), // soft corners
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black26,
                                blurRadius: 4,
                                offset: Offset(2, 2),
                              ),
                            ],
                          ),
                          child: Stack(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: AspectRatio(
                                  aspectRatio: 1,
                                  child: CustomPaint(
                                    painter: PuzzleTilePainter(
                                      image: fullImage!,
                                      srcRect: Rect.fromLTWH(
                                        dx,
                                        dy,
                                        tileWidth,
                                        tileHeight,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Align(
                                alignment: Alignment.bottomRight,
                                child: Padding(
                                  padding: const EdgeInsets.all(6.0),
                                  child: Text(
                                    '${tileNumber + 1}', // optional number
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      backgroundColor: Colors.black38,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          setState(() {
            tileOrder = List.generate(
              gridSize * gridSize,
              (i) => i,
            ); // ordered tiles
            emptyTile = tileOrder.last;
          });
        },
        label: Text("Solve Puzzle"),
        icon: Icon(Icons.check),
      ),
    );
  }
}

class PuzzleTilePainter extends CustomPainter {
  final ui.Image image;
  final Rect srcRect;

  PuzzleTilePainter({required this.image, required this.srcRect});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final dstRect = Offset.zero & size; // full size of the tile
    canvas.drawImageRect(image, srcRect, dstRect, paint); // draw part of image
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
